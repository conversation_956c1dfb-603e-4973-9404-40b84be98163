import {useCallback, useContext, useEffect, useMemo, useState} from 'react';
import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalCreateName from '@components/MetaCreateModal/ModalCreateName';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import CatalogWorkspace from '../components/CatalogWorkspace';
import IconSvg from '@components/IconSvg';
import {OutlinedPlusNew} from 'acud-icon';
import {RULE} from '@utils/regs';
import {useRequest} from 'ahooks';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType} from '@api/metaRequest';
import PermissionManage from '../../../components/PermissionManage';
import {ResourceType, Privilege} from '@api/permission/type';
import {isBuildInCatalog} from '../helper';
import {Button, Alert, Loading} from 'acud';
import {WorkspaceContext} from '@pages/index';
import {edapIframeSrc} from '@components/IframePreloader';
import forbiddenImg from '@assets/png/meta/forbidden.png';
import {CatalogConfig, DorisCatalogConfig} from '@components/PermissionManage/ constants';

export enum PanelEnum {
  DETAIL = '1',
  WORKSPACE = '2',
  PERMISSION = '3'
}

const EmptyPanel = (props: {catalog: string; catalogId: string}) => {
  const {catalog, catalogId} = props;
  const {workspaceId} = useContext(WorkspaceContext);
  const {data: catalogList, run: runCatalogList} = useRequest(http.getCatalogWorkspaceList, {
    defaultParams: [workspaceId, catalogId, {pageSize: 10000}],
    manual: true
  });

  useEffect(() => {
    catalogId && runCatalogList(workspaceId, catalogId, {pageSize: 10000});
  }, [catalogId]);

  return (
    <div className="empty-panel">
      <img className="empty-panel-img" src={forbiddenImg} />
      <p className="empty-panel-title">当前工作空间无权访问所选{catalog}数据目录</p>
      <p className="empty-panel-text">
        所属空间：{catalogList?.result?.items?.[0]?.workspaceName}，ID：
        {catalogList?.result?.items?.[0]?.workspaceId}
      </p>
    </div>
  );
};

const PanelCatalog = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList} = props;
  const {catalog = '', tab = PanelEnum.DETAIL, isUnassigned = false, isDoris = false} = urlState;
  const {workspaceId} = useContext(WorkspaceContext);

  // catalog loading
  const [loading, setLoading] = useState<boolean>(false);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<{
    catalog?: http.ICatalogDetailRes;
    metadataSummary?: http.ICatalogSummaryRes;
  }>({});

  const [authList, setAuthList] = useState<Privilege[]>([]); // 权限点列表

  // catalog 全名
  const fullName = `${catalog}`;

  // 获取详情
  const getCatalogDetail = useCallback(async () => {
    setLoading(true);
    setDataInfo({});
    const res = await http.getCatalogDetail(workspaceId, fullName);
    setDataInfo(res.result || {});
    // 当前登录用户是否是当前 Catalog 的 owner
    setAuthList(res.result?.catalog?.privileges || []);
    setLoading(false);
  }, [fullName, workspaceId]);

  // 初始化
  useEffect(() => {
    catalog && getCatalogDetail();
  }, [catalog, getCatalogDetail]);

  const catalogType = useMemo(() => dataInfo?.catalog?.type, [dataInfo?.catalog?.type]);

  const dropdownMenu = useMemo(() => {
    // 只有 datalake 类型可以重命名和删除
    const isEdit = catalogType && catalogType === CatalogType.DATALAKE;
    const disabled = !isEdit;
    return catalog
      ? [
          {
            key: 'rename',
            label: `重命名${MetaCnNameMap['Catalog']}`,
            disabled,
            authName: Privilege.Manage
          },
          {
            key: 'remove',
            label: `删除${MetaCnNameMap['Catalog']}`,
            disabled,
            authName: Privilege.Manage
          }
        ]
      : [];
  }, [catalogType, catalog]);

  /**
   * 1、默认 system  和 EDAPDataLake 无工作空间
   * 2、只有DATALAKE 类型 并且是【catalog owner】 或【元存储管理员】才可看到到「工作空间tab」
   **/
  const panelsList = useMemo(() => {
    const hasWorkspace = catalogType === CatalogType.DATALAKE && catalog !== CatalogType.SYSTEM;

    // 如果无权限，只显示工作空间 后端已经鉴权过了
    if (isUnassigned) {
      return [...(isDoris ? [] : [{tab: '工作空间', key: PanelEnum.WORKSPACE}])];
    }

    // 过滤掉工作空间
    return [
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(hasWorkspace ? [{tab: '工作空间', key: PanelEnum.WORKSPACE, privilege: [Privilege.Manage]}] : []),
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ];
  }, [catalogType, isUnassigned, catalog, isDoris]);

  // 详情字段类型配置
  const catalogInfoFieldsMap = useMemo(
    () => ({
      [CatalogType.EDAP_DATALAKE]: [
        'catalogName',
        'createdAt',
        'createdBy',
        'updatedAt',
        'updatedBy',
        'catalogType'
      ],
      [CatalogType.DORIS]: [
        'catalogName',
        'createdAt',
        'createdBy',
        'storageLocation',
        'updatedAt',
        'updatedBy',
        'catalogType',
        'computeName',
        'computeId'
      ],
      [CatalogType.DATALAKE]: [
        'catalogName',
        'createdAt',
        'createdBy',
        'storageLocation',
        'updatedAt',
        'updatedBy',
        'tableCount',
        'catalogType',
        'volumeCount',
        'operatorCount',
        'datasetCount',
        'modelCount'
      ]
      // 其他类型可按需补充
    }),
    []
  );

  const infoList = useMemo(() => {
    const info: any = dataInfo?.catalog || {};
    // 获取要展示的字段 list，如找不到默认使用 DATALAKE 类型
    const fields = catalogInfoFieldsMap[catalogType] || catalogInfoFieldsMap[CatalogType.DATALAKE];
    const catalogShowNameMap = {
      [CatalogType.DATALAKE]: 'Datalake 数据目录',
      [CatalogType.DORIS]: '分析与AI搜索实例-Doris 数据目录',
      [CatalogType.EDAP_DATALAKE]: 'EDAPDataLake 数据目录'
    };
    return fields
      .map((key) => {
        switch (key) {
          case 'catalogName':
            return {
              label: `${MetaCnNameMap['Catalog']}名称`,
              value: info.name
            };
          case 'createdAt':
            return {
              label: '创建时间',
              value: info.createdAt
            };
          case 'createdBy':
            return {
              label: '创建人',
              value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
            };
          case 'storageLocation':
            return {
              label: '存储路径',
              value: info.storageLocation
            };
          case 'updatedAt':
            return {
              label: '修改时间',
              value: info.updatedAt
            };
          case 'updatedBy':
            return {
              label: '最近修改人',
              value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
            };

          case 'tableCount':
            return {
              label: `${MetaCnNameMap['Table']}个数`,
              value: `${dataInfo?.metadataSummary?.tableCount || 0}个`
            };
          case 'catalogType':
            return catalog === CatalogType.SYSTEM // 特殊点：根据名称区别对待
              ? null
              : {
                  label: `${MetaCnNameMap['Catalog']}类型`,
                  value: catalogShowNameMap[catalogType]
                };
          case 'computeName':
            return {
              label: '分析与AI搜索实例名称',
              value: dataInfo.catalog?.properties?.computeName
            };
          case 'computeId':
            return {
              label: '分析与AI搜索实例ID',
              value: dataInfo.catalog?.properties?.computeId
            };
          case 'volumeCount':
            return {
              label: `${MetaCnNameMap['Volume']}个数`,
              value: `${dataInfo?.metadataSummary?.volumeCount || 0}个`
            };
          case 'operatorCount':
            return {
              label: `${MetaCnNameMap['Operator']}个数`,
              value: `${dataInfo?.metadataSummary?.operatorCount || 0}个`
            };
          case 'datasetCount':
            return {
              label: `${MetaCnNameMap['Dataset']}个数`,
              value: `${dataInfo?.metadataSummary?.datasetCount || 0}个`
            };
          case 'modelCount':
            return {
              label: `${MetaCnNameMap['Model']}个数`,
              value: `${dataInfo?.metadataSummary?.modelCount || 0}个`
            };
          default:
            return null;
        }
      })
      .filter(Boolean);
  }, [catalog, catalogType, dataInfo, userList, catalogInfoFieldsMap]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchCatalog(workspaceId, fullName, {comment: text});
      getCatalogDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, catalog: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, catalog: CatalogType.SYSTEM}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: catalog,
          title: 'Catalog',
          requestFun: http.deleteCatalog,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      }
    },
    [catalog, removeSuccessFun]
  );

  // 新建 Schema 弹窗
  const [showCreateModal, setCreateModal] = useState<boolean>(false);
  const onCreateClick = useCallback(() => {
    setCreateModal(true);
  }, []);
  const createSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFun((preState) => ({...preState, tab: '1', schema: formData.name}));
      handleTreeRefresh && handleTreeRefresh();
    },
    [changeUrlFun]
  );

  const createSchemaNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getSchemaDetail(workspaceId, `${catalog}.${value}`, true);
          if (res.success && res.result?.schema?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Schema']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType, workspaceId]);

  const renderTab = useMemo(() => {
    const config = {
      // Tab-Panel 详情
      [PanelEnum.DETAIL]: (
        <>
          <DescriptionEdit
            text={dataInfo?.catalog?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
            authList={authList}
          />

          <InfoPanel infoList={infoList} title="基本信息" />
        </>
      ),
      // 权限管理
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Catalog}
          resourceId={fullName}
          hasInheritedFrom
          name={catalog}
          config={isDoris ? DorisCatalogConfig : CatalogConfig}
          onSuccess={getCatalogDetail}
          manageDescription={isDoris ? '授予对象类似所有者的权限，例如权限管理' : ''}
        />
      ),
      // 工作空间
      [PanelEnum.WORKSPACE]: (
        <div className="catalog-panel-workspace">
          <CatalogWorkspace
            catalogName={catalog}
            catalog={dataInfo?.catalog}
            changeUrlFunReplace={changeUrlFunReplace}
          />
        </div>
      )
    };
    return config[tab];
  }, [
    dataInfo?.catalog,
    onChangeDescript,
    catalog,
    authList,
    infoList,
    fullName,
    isDoris,
    changeUrlFunReplace,
    tab,
    getCatalogDetail
  ]);

  const barIcon = useMemo(() => {
    const imageName = isDoris ? 'meta-doris' : 'meta-iceberg';
    if (catalog === CatalogType.SYSTEM) {
      return <IconSvg type="meta-system" size={20} color="#fff" />;
    }
    // 无权限图标
    else if (isUnassigned) {
      return <IconSvg type={`${imageName}-forbidden`} size={20} color="#fff" />;
    }
    // 有权限图标
    else {
      return <IconSvg type={imageName} size={20} color="#fff" />;
    }
  }, [catalog, isDoris, isUnassigned]);

  const hiddenCreate = catalogType === CatalogType.EDAP_DATALAKE || isUnassigned;

  return (
    <div className="work-meta-catalog-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={barIcon}
        title={catalog}
        dropdownMenu={isUnassigned ? [] : dropdownMenu}
        onDropdownClick={onDropdownClick}
        createText={`创建${MetaCnNameMap['Schema']}`}
        createIcon={<OutlinedPlusNew width={16} height={16} />}
        onCreateClick={onCreateClick}
        createAuthName={Privilege.CreateSchema}
        authList={authList}
        hiddenCreate={hiddenCreate}
        otherButton={
          catalogType === CatalogType.EDAP_DATALAKE ? (
            <Button
              onClick={() =>
                window.open(`${edapIframeSrc}#/meta-data/manage?type=EDAP&topic=DataLake`, '_blank')
              }
            >
              去管理
            </Button>
          ) : null
        }
      />
      {panelsList.length > 0 && isUnassigned && (
        <Alert
          message="用户无法从当前工作空间访问所选数据目录，请使用下方界面配置哪些工作空间可以访问该数据目录。"
          banner
          className="mb-[8px]"
        />
      )}
      {/* Tabs */}
      <MetaTabs panesList={panelsList} tab={tab} onTabChange={onTabChange} authList={authList} />
      {/* tab content */}
      {panelsList.length > 0 ? (
        <Loading loading={loading}>{renderTab}</Loading>
      ) : (
        <EmptyPanel catalog={catalog} catalogId={dataInfo.catalog?.id || ''} />
      )}
      {/** 创建 Schema 弹窗 */}
      <ModalCreateName
        visible={showCreateModal}
        onCancel={() => setCreateModal(false)}
        title="Schema"
        requestFun={http.createSchema}
        requestParams={{catalogName: catalog, workspaceId}}
        successCallback={createSuccessFun}
        nameRules={createSchemaNameRules}
        showDescription={catalogType !== CatalogType.DORIS}
      />
      {/** 重命名 Catalog 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={catalog}
        title="Catalog"
        requestFun={http.patchCatalog}
        successFun={renameSuccessFun}
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              // const res = await http.getCatalogDetail(workspaceId, `${value}`, true);
              // if (res.success && res.result?.catalog?.id) {
              //   return Promise.reject(new Error(`该${MetaCnNameMap['Catalog']}名称已存在，请重新输入`));
              // }
              return Promise.resolve();
            }
          }
        ]}
      />
    </div>
  );
};
export default PanelCatalog;
